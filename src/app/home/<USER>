'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import dynamic from 'next/dynamic';
import { Toaster } from 'react-hot-toast';

// Interfaces
export interface CompanyData {
  id: number;
  name: string;
  legal_name: string;
  description: string;
  address: string;
  city: string;
  country: string;
  phone: string;
  email: string;
  website: string;
  logo_url: string;
  logo_image_url: string;
  established_date: string;
  registration_number: string;
  legal_form: string;
  capital: number;
  tax_number: string;
  is_active: boolean;
  working_hours?: string;
}

interface Stat {
  clients: number;
  issues: number;
  employees: number;
  completedIssues: number;
  newIssues: number;
  courts: number;
  successRate: number;
  experienceYears: number;
}

// Mock data
const companyData: CompanyData = {
  id: 1,
  name: 'مكتب المحاماة والاستشارات القانونية',
  legal_name: 'المكتب الاستشاري القانوني المتكامل',
  description: 'حلول قانونية متكاملة باحترافية عالية',
  address: 'صنعاء - شارع الزبيري',
  city: 'صنعاء',
  country: 'اليمن',
  phone: '+967 1 234 567',
  email: '<EMAIL>',
  website: 'www.lawfirm.com',
  logo_url: '',
  logo_image_url: '/images/logo.png',
  established_date: '2010-01-01',
  registration_number: '*********',
  legal_form: 'شركة ذات مسؤولية محدودة',
  capital: 1000000,
  tax_number: '*********',
  is_active: true,
  working_hours: 'الأحد - الخميس: 8 صباحاً - 4 مساءً'
};

const stats: Stat = {
  clients: 1200,
  issues: 5000,
  employees: 25,
  completedIssues: 4900,
  newIssues: 100,
  courts: 15,
  successRate: 98,
  experienceYears: 15
};

// Dynamic imports for components
const AnnouncementBar = dynamic(
  () => import('./components/announcement-bar').then((mod) => mod.AnnouncementBar),
  { ssr: false, loading: () => null }
);

const HeaderComponent = dynamic(
  () => import('./components/header').then((mod) => mod.Header),
  { ssr: false, loading: () => null }
);

const HeroSection = dynamic(
  () => import('./components/hero-section').then((mod) => mod.HeroSection),
  { ssr: false, loading: () => null }
);

const ServicesSection = dynamic(
  () => import('./components/services-section').then((mod) => mod.ServicesSection),
  { ssr: false, loading: () => null }
);

const AboutSection = dynamic(
  () => import('./components/about-section').then((mod) => mod.AboutSection),
  { ssr: false, loading: () => null }
);

const StatisticsSection = dynamic(
  () => import('./components/statistics-section').then((mod) => mod.StatisticsSection),
  { ssr: false, loading: () => null }
);

const TestimonialsSection = dynamic(
  () => import('./components/testimonials-section').then((mod) => mod.TestimonialsSection),
  { ssr: false, loading: () => null }
);

const ContactSection = dynamic(
  () => import('./components/contact-section').then((mod) => mod.ContactSection),
  { ssr: false, loading: () => null }
);

const Footer = dynamic(
  () => import('./components/footer').then((mod) => mod.Footer),
  { ssr: false, loading: () => null }
);

export default function HomePage() {
  const router = useRouter();
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const [isChatWidgetOpen, setIsChatWidgetOpen] = useState(false);
  const [activeAnnouncement, setActiveAnnouncement] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');

  const announcements = [
    {
      id: 1,
      title: "إعلان هام",
      content: "المكتب مفتوح الآن لاستقبال الاستشارات القانونية",
      type: "primary" as const,
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: 2,
      title: "عرض خاص",
      content: "خصم 20% على رسوم الاستشارة الأولى",
      type: "secondary" as const,
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: 3,
      title: "ندوة مجانية",
      content: "حقوقك القانونية في العمل",
      type: "primary" as const,
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  ];

  const handleAnnouncementChange = (id: number | null) => {
    setActiveAnnouncement(id || 1);
  };

  const scrollToContact = () => {
    const contactSection = document.getElementById('contact');
    contactSection?.scrollIntoView({ behavior: 'smooth' });
  };

  const scrollToServices = () => {
    const servicesSection = document.getElementById('services');
    servicesSection?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <div dir="rtl" className="min-h-screen bg-white text-gray-900">
      <Toaster />

      {/* Announcement Bar */}
      <AnnouncementBar
        announcements={announcements}
        activeAnnouncement={activeAnnouncement}
        onAnnouncementChange={handleAnnouncementChange}
      />

      {/* Header */}
      <HeaderComponent
        companyData={companyData}
        onLoginClick={() => setIsLoginModalOpen(true)}
        onContactClick={scrollToContact}
      />

      <main>
        {/* Hero Section */}
        <HeroSection
          companyData={companyData}
          stats={stats}
          onContactClick={scrollToContact}
          onServicesClick={scrollToServices}
        />

        {/* Services Section */}
        <section id="services" className="py-16 bg-gray-50">
          <ServicesSection
            searchQuery={searchQuery}
            onSearch={setSearchQuery}
          />
        </section>

        {/* About Section */}
        <section className="py-16">
          <AboutSection />
        </section>

        {/* Statistics Section */}
        <section className="py-16 bg-gray-50">
          <StatisticsSection stats={stats} />
        </section>

        {/* Testimonials Section */}
        <section className="py-16">
          <TestimonialsSection />
        </section>

        {/* Contact Section */}
        <section id="contact" className="py-16 bg-gray-50">
          <ContactSection />
        </section>
      </main>

      {/* Footer */}
      <Footer companyData={companyData} />

      {/* Login Modal */}
      {isLoginModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-8 rounded-lg w-full max-w-md">
            <h2 className="text-2xl font-bold mb-4">تسجيل الدخول</h2>
            <form>
              <div className="mb-4">
                <label className="block text-gray-700 mb-2">البريد الإلكتروني</label>
                <input
                  type="email"
                  className="w-full p-2 border rounded"
                  placeholder="أدخل بريدك الإلكتروني"
                />
              </div>
              <div className="mb-4">
                <label className="block text-gray-700 mb-2">كلمة المرور</label>
                <input
                  type="password"
                  className="w-full p-2 border rounded"
                  placeholder="أدخل كلمة المرور"
                />
              </div>
              <div className="flex justify-between items-center">
                <button
                  type="button"
                  className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
                >
                  تسجيل الدخول
                </button>
                <button
                  type="button"
                  className="text-gray-600"
                  onClick={() => setIsLoginModalOpen(false)}
                >
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Chat Widget */}
      <button
        className="fixed bottom-8 left-8 bg-green-600 text-white p-4 rounded-full shadow-lg hover:bg-green-700 transition-colors"
        onClick={() => setIsChatWidgetOpen(!isChatWidgetOpen)}
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
        </svg>
      </button>

      {isChatWidgetOpen && (
        <div className="fixed bottom-24 left-8 w-80 bg-white rounded-lg shadow-xl overflow-hidden">
          <div className="bg-green-600 text-white p-4 flex justify-between items-center">
            <h3 className="font-bold">الدردشة المباشرة</h3>
            <button onClick={() => setIsChatWidgetOpen(false)}>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
          <div className="p-4 h-64 overflow-y-auto">
            <div className="text-center text-gray-500 text-sm py-8">
              مرحباً! كيف يمكننا مساعدتك اليوم؟
            </div>
          </div>
          <div className="p-4 border-t">
            <div className="flex">
              <input
                type="text"
                className="flex-1 border rounded-r-none p-2 focus:outline-none"
                placeholder="اكتب رسالتك..."
                dir="rtl"
              />
              <button className="bg-green-600 text-white px-4 rounded-l-none hover:bg-green-700">
                إرسال
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}